// 与 iOS WeatherAPIService 对齐的 Web 适配层（开发期实现）
// 注意：仅实现当前天气与7天预报，城市名->locationId 暂用精简映射，后续接入 POI/Geo API。
// 不新增 iOS 没有的功能，字段与 iOS 尽量一致。

export interface ICurrentWeather {
  obsTime: string
  temp: string
  feelsLike: string
  icon: string
  text: string
  windDir: string
  windScale: string
  humidity: string
}

// 天气预警
export async function fetchWeatherWarnings(locationId: string): Promise<IWeatherWarning[]> {
  const url = `${baseURL}/warning/now?location=${encodeURIComponent(locationId)}&key=${apiKey}`
  const res = await fetch(url)
  if (!res.ok) return []
  const data = await res.json()
  if (data.code !== '200') return []
  const arr = Array.isArray(data.warning) ? data.warning : []
  return arr.map((w: unknown) => ({
    title: String((w as { title?: string })?.title ?? ''),
    level: (w as { level?: string })?.level ? String((w as { level: string }).level) : undefined,
    startTime: (w as { startTime?: string })?.startTime ? String((w as { startTime: string }).startTime) : undefined,
    endTime: (w as { endTime?: string })?.endTime ? String((w as { endTime: string }).endTime) : undefined,
  })).filter((w: IWeatherWarning) => w.title)
}

// 空气质量
export async function fetchAirQuality(locationId: string): Promise<IAirQuality | null> {
  const url = `${baseURL}/air/now?location=${encodeURIComponent(locationId)}&key=${apiKey}`
  const res = await fetch(url)
  if (!res.ok) return null
  const data = await res.json()
  // 部分套餐无 code 字段，兼容直接读取 now
  const now = (data as { now?: unknown }).now
  if (!now) return null
  const aq: IAirQuality = {
    aqi: String((now as { aqi?: string }).aqi ?? ''),
    level: (now as { level?: string }).level ? String((now as { level: string }).level) : undefined,
    category: (now as { category?: string }).category ? String((now as { category: string }).category) : undefined,
    primary: (now as { primary?: string }).primary ? String((now as { primary: string }).primary) : undefined,
    pm2p5: (now as { pm2p5?: string }).pm2p5 ? String((now as { pm2p5: string }).pm2p5) : undefined,
    pm10: (now as { pm10?: string }).pm10 ? String((now as { pm10: string }).pm10) : undefined,
  }
  return aq.aqi ? aq : null
}

// 24小时逐时
export async function fetchHourlyWeather(locationId: string): Promise<IHourlyWeather[]> {
  const url = `${baseURL}/weather/24h?location=${encodeURIComponent(locationId)}&key=${apiKey}`
  const res = await fetch(url)
  if (!res.ok) return []
  const data = await res.json()
  if (data.code && data.code !== '200') return []
  const arr = Array.isArray(data.hourly) ? data.hourly : []
  return arr as IHourlyWeather[]
}

export interface IDailyWeather {
  fxDate: string
  tempMax: string
  tempMin: string
  textDay: string
  textNight: string
}

// 预警（对齐 iOS 需要的最小字段）
export interface IWeatherWarning {
  title: string
  level?: string
  startTime?: string
  endTime?: string
}

// 空气质量（最小字段集）
export interface IAirQuality {
  aqi: string
  level?: string
  category?: string
  primary?: string
  pm2p5?: string
  pm10?: string
}

// 24小时逐时（最小字段集）
export interface IHourlyWeather {
  fxTime: string
  temp: string
  text: string
  windDir?: string
  windScale?: string
  humidity?: string
  pop?: string
  precip?: string
}

// 与 iOS 中相同的 baseURL（可由环境变量覆盖）
const baseURL = (import.meta as { env?: { VITE_QWEATHER_BASE?: string } })?.env?.VITE_QWEATHER_BASE || 'https://devapi.qweather.com/v7'
// API Key 读取自环境变量（开发占位回退）；生产请放服务端
const apiKey = (import.meta as { env?: { VITE_QWEATHER_KEY?: string } })?.env?.VITE_QWEATHER_KEY || 'a798b095fc7347939e6d8bd12f1feb91'

// 精简的城市到 HeWeather locationId 映射（后续接入 POI/Geo API）
const CITY_TO_LOCATION_ID: Record<string, string> = {
  北京: '101010100',
  上海: '101020100',
  广州: '101280101',
  深圳: '101280601',
}

export function getLocationIdForCity(city: string): string | undefined {
  const key = city.trim()
  return CITY_TO_LOCATION_ID[key]
}

export async function fetchCurrentWeather(locationId: string): Promise<ICurrentWeather | null> {
  try {
    const url = `${baseURL}/weather/now?location=${encodeURIComponent(locationId)}&key=${apiKey}`
    const res = await fetch(url)
    if (!res.ok) throw new Error('API请求失败')
    const data = await res.json()
    if (data.code !== '200') throw new Error('API返回错误')
    return data.now as ICurrentWeather
  } catch (error) {
    console.warn('天气API调用失败，使用模拟数据:', error)
    // 返回模拟数据
    return {
      obsTime: new Date().toISOString(),
      temp: '22',
      feelsLike: '24',
      icon: '100',
      text: '晴',
      windDir: '东南风',
      windScale: '2级',
      humidity: '45'
    }
  }
}

export async function fetchWeatherForecast7d(locationId: string): Promise<IDailyWeather[]> {
  const url = `${baseURL}/weather/7d?location=${encodeURIComponent(locationId)}&key=${apiKey}`
  const res = await fetch(url)
  if (!res.ok) return []
  const data = await res.json()
  if (data.code !== '200') return []
  return data.daily as IDailyWeather[]
}

// 基于经纬度的查询（对齐 QWeather 支持的 "lon,lat" 形式）
export async function fetchCurrentWeatherByCoord(lat: number, lon: number): Promise<ICurrentWeather | null> {
  try {
    const loc = `${lon},${lat}`
    const url = `${baseURL}/weather/now?location=${encodeURIComponent(loc)}&key=${apiKey}`
    const res = await fetch(url)
    if (!res.ok) throw new Error('GPS天气API请求失败')
    const data = await res.json()
    if (data.code !== '200') throw new Error('GPS天气API返回错误')
    return data.now as ICurrentWeather
  } catch (error) {
    console.warn('GPS天气API调用失败，使用模拟数据:', error)
    // 返回模拟GPS天气数据
    return {
      obsTime: new Date().toISOString(),
      temp: '20',
      feelsLike: '22',
      icon: '101',
      text: '多云',
      windDir: '西北风',
      windScale: '3级',
      humidity: '52'
    }
  }
}

export async function fetchWeatherForecast7dByCoord(lat: number, lon: number): Promise<IDailyWeather[]> {
  const loc = `${lon},${lat}`
  const url = `${baseURL}/weather/7d?location=${encodeURIComponent(loc)}&key=${apiKey}`
  const res = await fetch(url)
  if (!res.ok) return []
  const data = await res.json()
  if (data.code !== '200') return []
  return data.daily as IDailyWeather[]
}
