// 备忘录地点抽取（Web）——对齐 iOS 逻辑，严格不新增 iOS 没有的功能
// 规则来源：WeatherNotablesView.swift 中的提取思路（预警正则、时间关键词切分、去天气词、长度限制、兜底）
// 说明：解析时通过动词短语提取候选城市，并使用 CityResolver/CITY_TOP100 进行校验，避免将整句当作城市名。

import { CITY_TOP100 } from '@/adapters/cityTop100'
import { cityResolver } from './CityResolver'

const TIME_SPLIT_TOKENS = [
  '现在', '今日', '今天', '今夜', '明天', '后天', '未来', '降水概率',
]

// 常见天气词尾，需从城市候选名末尾清理
const TRAILING_WEATHER_WORDS = [
  '小雨', '中雨', '大雨', '阵雨', '雷阵雨', '暴雨', '雨',
  '小雪', '中雪', '大雪', '阵雪', '暴雪', '雪',
  '大风', '强风', '风', '台风',
  '雾', '霾', '沙尘', '扬沙', '浮尘',
  '晴', '阴', '多云', '云',
  '雷', '电', '冰', '霜', '露', '雹',
]

// 预警正则（示例）：匹配“北京黄色暴雨预警”、“上海高温红色预警”等
// 该正则用于从一句话中快速抓取可能的城市片段（城市名应在前）
const WARNING_REGEXES: RegExp[] = [
  /([\u4e00-\u9fa5]{2,10}).{0,6}预警/, // 宽松匹配：前 2-10 个汉字作为城市候选
]

// 常见动词短语捕获城市名：如 “住在北京”“在哈尔滨下雪了”“去兰州旅游了”“到上海出差”
const VERB_CITY_REGEXES: RegExp[] = [
  /(住在|在|去|到)([\u4e00-\u9fa5]{2,10})/,
]

function splitByTimeKeywords(text: string): string {
  // eslint-disable-next-line no-restricted-syntax
  for (const token of TIME_SPLIT_TOKENS) {
    const idx = text.indexOf(token)
    if (idx > 0) {
      return text.slice(0, idx)
    }
  }
  return text
}

function stripPunctuations(s: string): string {
  return s.replace(/[：:，,。.!！?？\-—_~·•\s]+$/g, '')
}

function removeTrailingWeatherWords(s: string): string {
  let out = s
  // eslint-disable-next-line no-restricted-syntax
  for (const w of TRAILING_WEATHER_WORDS) {
    if (out.endsWith(w)) {
      out = out.slice(0, -w.length)
      // 可能存在连续的天气词，继续循环
      return removeTrailingWeatherWords(out)
    }
  }
  return out
}

function cleanCityCandidate(raw: string): string {
  let s = raw.trim()
  s = splitByTimeKeywords(s)
  s = stripPunctuations(s)
  s = removeTrailingWeatherWords(s)
  s = s.trim()
  if (s.length > 10) s = s.slice(0, 10)
  return s
}

function tryExtractCityByWarning(text: string): string | '' {
  // eslint-disable-next-line no-restricted-syntax
  for (const re of WARNING_REGEXES) {
    const m = text.match(re)
    if (m && m[1]) {
      const candidate = cleanCityCandidate(m[1])
      if (candidate) return candidate
    }
  }
  return ''
}

export function extractCitiesFromMemos(memos: string[]): string[] {
  const results: string[] = []
  const seen = new Set<string>()

  // eslint-disable-next-line no-restricted-syntax
  for (const memo of memos) {
    // eslint-disable-next-line no-continue
    if (!memo || typeof memo !== 'string') continue
    const raw = memo.trim()
    // eslint-disable-next-line no-continue
    if (!raw) continue

    // 1) 预警类优先
    let city = tryExtractCityByWarning(raw)

    // 2) 动词短语抽取 + 清洗
    if (!city) {
      // eslint-disable-next-line no-restricted-syntax
      for (const re of VERB_CITY_REGEXES) {
        const m = raw.match(re)
        if (m && m[2]) {
          city = cleanCityCandidate(m[2])
          if (city) break
        }
      }
    }

    // 3) 扫描 Top100 名称是否出现在原句中（避免整句当城市名）
    if (!city) {
      // eslint-disable-next-line no-restricted-syntax
      for (const name of Object.keys(CITY_TOP100)) {
        if (raw.includes(name)) {
          city = name
          break
        }
      }
    }

    // 4) 常规兜底：按时间关键词切分 + 清洗（不强制同步验证，留给后续 CityResolver 懒加载）
    if (!city) {
      const candidate = cleanCityCandidate(raw)
      if (candidate) city = candidate
    }

    // 5) 验证放宽：此处不做同步校验，交由 WeatherService.resolveLocationIdForCity 懒加载解析
    if (!city) continue

    if (!seen.has(city)) {
      seen.add(city)
      results.push(city)
    }
  }

  return results
}

export function cleanCityNameFromWeatherWords(city: string): string {
  return removeTrailingWeatherWords(stripPunctuations(city)).trim()
}
