<template>
  <section class="notables-view warm-card">
    <div class="header">
      <div class="left">
        <span class="title">划重点</span>
        <span class="subtitle">信息太多先抓重点</span>
      </div>
      <div class="right">
        <button class="icon-btn" title="语音播放"><PlayIcon :size="12" /></button>
        <button class="icon-btn wand" title="小魔仙">✨</button>
      </div>
    </div>

    <!-- 城市 Tabs：全部 + 每个城市 -->
    <div v-if="tabs.length" class="tabs">
      <button
        v-for="t in tabs"
        :key="t"
        class="tab"
        :class="{ active: t === selectedTab }"
        @click="selectedTab = t"
      >{{ t }}</button>
    </div>

    <div v-if="loading">
      <!-- 优先展示加载步骤（与“深解读”一致） -->
      <div v-if="loadingSteps && loadingSteps.length" class="steps">
        <div v-for="(step, i) in loadingSteps" :key="i" class="step">
          <span class="step-icon" :class="{ pending: step.includes('正在') }">{{ step.includes('正在') ? '…' : '✓' }}</span>
          <span class="step-text">{{ step }}</span>
        </div>
      </div>
      <!-- 回退：骨架占位 -->
      <div v-else class="skeleton">
        <div v-for="i in 3" :key="i" class="sk-line"></div>
      </div>
    </div>

    <template v-else>
      <div class="card-list">
        <NotableCityCard v-for="g in filteredGroups" :key="g.city" :city="g.city" :notices="g.notices" />
      </div>
      <div v-if="!groups || groups.length === 0" class="empty">{{ props.emptyReason || '暂无可用的划重点，请在备忘录添加地点' }}</div>
    </template>
  </section>
  
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import NotableCityCard from './NotableCityCard.vue'
import PlayIcon from '@/components/common/PlayIcon.vue'

interface IGroupItem { city: string; notices: string[] }

interface IProps {
  groups: IGroupItem[]
  loading?: boolean
  loadingSteps?: string[]
  emptyReason?: string
}

const props = defineProps<IProps>()

const tabs = computed(() => {
  const set = new Set<string>()
  for (const g of props.groups || []) if (g?.city) set.add(g.city)
  const arr = Array.from(set)
  // 无城市数据时不渲染 Tabs（不显示“全部”）
  return arr.length ? ['全部', ...arr] : []
})
const selectedTab = ref('全部')
const filteredGroups = computed(() => {
  if (selectedTab.value === '全部') return props.groups || []
  return (props.groups || []).filter(g => g.city === selectedTab.value)
})
</script>

<style scoped>
.notables-view { padding: 20px; }
.header { display:flex; align-items:center; gap:12px; padding:16px 20px; border-radius:20px; border:2px solid var(--border-accent); background: var(--bg-glass); backdrop-filter: blur(10px); transition: all 0.3s ease; }
.header:hover { background: var(--bg-glass-hover); transform: translateY(-1px); }
.left { display:flex; align-items:baseline; gap:8px; }
.right { margin-left:auto; display:flex; gap:6px; }
.title { font-size: 18px; font-weight: 700; color: #000000; }
.subtitle { font-size: 14px; color: #000000; font-weight:600; opacity: 0.8; }
.icon-btn { width:20px; height:20px; border-radius:10px; border:1px solid rgba(0,0,0,0.06); background: rgba(255,255,255,0.9); color:#2e7d32; display:inline-flex; align-items:center; justify-content:center; -webkit-appearance: none; appearance: none; line-height: 0; }
.icon-btn .play-icon { display:block; fill: currentColor; }
.icon-btn.wand { color:#DAA520 }
.steps { display:flex; flex-direction:column; gap:8px; padding: 8px 0; }
.step { display:flex; align-items:center; gap:8px; }
.step-icon { width:16px; display:inline-flex; align-items:center; justify-content:center; color: #000000; }
.step-icon.pending { color: var(--primary, #f90); }
.step-text { font-size: 13px; color: #000000; }
.skeleton { display:flex; flex-direction:column; gap:8px; padding:6px 0; }
.sk-line { height: 12px; background: var(--skeleton); border-radius: 6px; opacity: 0.7; }
.empty { font-size: 13px; color: #000000; padding: 8px 0; }
.card-list { display:flex; flex-direction:column; gap:8px; }

/* Tabs */
.tabs { display:flex; gap:6px; padding:6px 0 8px; flex-wrap:wrap }
.tab { font-size:12px; padding:4px 10px; border-radius:999px; border:1px solid var(--border); background: var(--card-bg); color: #000000; }
.tab.active { background: linear-gradient(180deg, rgba(240,248,255,0.9), rgba(240,248,255,0.7)); border-color: rgba(0,0,0,0.08); }

/* 简易图标占位，与其他模块保持一致（播放图标改为 PlayIcon 组件） */
.i-wand { width:12px; height:12px; display:inline-block; position:relative; background: transparent }
.i-wand { position:relative }
.i-wand::before { content:""; position:absolute; left:5px; top:1px; width:2px; height:10px; background: currentColor; transform: rotate(28deg); border-radius:2px }
.i-wand::after { content:""; position:absolute; left:2px; top:0; width:4px; height:4px; border-radius:50%; background: currentColor; opacity:.9 }
</style>
